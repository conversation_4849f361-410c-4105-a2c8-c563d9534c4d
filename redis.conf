# Configuração do Redis para o wplace-proxy

# Configurações básicas
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300

# Configurações de memória
maxmemory 256mb
maxmemory-policy allkeys-lru

# Configurações de persistência
save 900 1
save 300 10
save 60 10000

# Configurações de log
loglevel notice
logfile ""

# Configurações de segurança
# requirepass your-redis-password-here

# Configurações de performance
tcp-backlog 511
databases 16

# Configurações para JSON
# O Redis Stack já inclui o módulo RedisJSON automaticamente
